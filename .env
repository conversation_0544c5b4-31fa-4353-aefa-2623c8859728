# 基础环境配置
# 应用标题
VITE_APP_TITLE=Chai Admin

# 应用版本
VITE_APP_VERSION=1.0.0

# 应用描述
VITE_APP_DESCRIPTION=基于Vue3 + Element Plus的后台管理系统

# 应用端口
VITE_PORT=5174

# API基础路径
VITE_API_BASE_URL=/api

# 是否开启mock
VITE_USE_MOCK=false

# 是否开启PWA
VITE_USE_PWA=false

# 是否开启CDN
VITE_USE_CDN=false

# CDN基础路径
VITE_CDN_URL=

# 路由模式 hash | history
VITE_ROUTER_MODE=history

# 日志级别 debug | info | warn | error
VITE_LOG_LEVEL=info

# 是否显示错误覆盖层
VITE_SHOW_ERROR_OVERLAY=true

# 上传文件大小限制（MB）
VITE_UPLOAD_SIZE_LIMIT=10

# 分页默认页大小
VITE_DEFAULT_PAGE_SIZE=10

# 分页最大页大小
VITE_MAX_PAGE_SIZE=1000

# 默认主题
VITE_DEFAULT_THEME=light

# 主色调
VITE_PRIMARY_COLOR=#409EFF

# 侧边栏宽度
VITE_SIDEBAR_WIDTH=200

# 侧边栏折叠宽度
VITE_SIDEBAR_COLLAPSED_WIDTH=64

# 头部高度
VITE_HEADER_HEIGHT=60

# 标签页高度
VITE_TABS_HEIGHT=40

# 是否启用路由缓存
VITE_ENABLE_ROUTE_CACHE=true

# 缓存策略 localStorage | sessionStorage | memory
VITE_CACHE_STRATEGY=localStorage

# 缓存前缀
VITE_CACHE_PREFIX=chai_admin_

# 是否开启gzip压缩
VITE_BUILD_GZIP=false

# 是否删除console
VITE_DROP_CONSOLE=false
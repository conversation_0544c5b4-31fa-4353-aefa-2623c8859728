# 开发环境配置
# 继承基础配置

# API基础路径
VITE_API_BASE_URL=http://localhost:8000/api

# 是否开启mock数据
VITE_USE_MOCK=false

# 是否开启代理
VITE_USE_PROXY=true

# 代理目标地址
VITE_PROXY_TARGET=http://localhost:8000

# 是否开启https
VITE_USE_HTTPS=false

# 是否自动打开浏览器
VITE_OPEN_BROWSER=true

# 是否开启热更新
VITE_HMR=true

# 开发服务器主机
VITE_HOST=0.0.0.0

# 是否显示详细错误信息
VITE_SHOW_ERROR_OVERLAY=true

# 是否开启source map
VITE_SOURCE_MAP=true

# 日志级别
VITE_LOG_LEVEL=info
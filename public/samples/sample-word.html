<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word示例文档</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
        }
        .highlight {
            background-color: #f1c40f;
            padding: 2px 4px;
        }
        .important {
            color: #e74c3c;
            font-weight: bold;
        }
        ul {
            margin: 15px 0;
        }
        li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>Word示例文档</h1>
    
    <h2>文档概述</h2>
    <p>这是一个用于演示Office文档预览功能的示例Word文档。本文档包含了常见的文档格式和内容结构。</p>
    
    <h2>主要功能特点</h2>
    <ul>
        <li><span class="highlight">支持多种文档格式</span>：Word、Excel、PowerPoint等</li>
        <li><span class="important">在线预览</span>：无需下载即可查看文档内容</li>
        <li>响应式设计：适配不同设备屏幕</li>
        <li>用户友好：简洁直观的操作界面</li>
    </ul>
    
    <h2>使用说明</h2>
    <p>1. 选择文件来源（本地示例、远程URL或上传文件）</p>
    <p>2. 根据选择的来源进行相应操作</p>
    <p>3. 点击预览按钮查看文档内容</p>
    
    <h2>技术实现</h2>
    <p>本功能基于Vue3 + Element Plus + vue-files-preview组件库实现，提供了完整的文档预览解决方案。</p>
    
    <p><em>注意：这是一个HTML格式的示例文件，实际使用时请替换为真实的Office文档。</em></p>
</body>
</html>
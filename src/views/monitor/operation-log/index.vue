<template>
  <div class="app-container">
    <el-card shadow="never">
      <template #header>
        <el-form :model="queryParams" ref="queryRef" :inline="true">
          <el-form-item label="系统模块" prop="title">
            <el-input
              v-model="queryParams.title"
              placeholder="请输入系统模块"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="操作人员" prop="userName">
            <el-input
              v-model="queryParams.userName"
              placeholder="请输入操作人员"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="类型" prop="businessType">
            <el-select
              v-model="queryParams.businessType"
              placeholder="操作类型"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="(item, index) in businessTypeOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="操作状态"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="(item, index) in statusOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="操作时间" prop="dateRange">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-time="[new Date('2000-01-01 00:00:00'), new Date('2000-01-01 23:59:59')]"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </template>

      <el-table
        v-loading="loading"
        :data="operlogList"
        border
      >
        <el-table-column label="日志编号" align="center" prop="id" width="100" />
        <el-table-column label="系统模块" align="center" prop="title" width="150" :show-overflow-tooltip="true" />
        <el-table-column label="操作类型" align="center" prop="businessType" width="100">
          <template #default="scope">
            <dict-tag :options="businessTypeOptions" :value="scope.row.businessType" />
          </template>
        </el-table-column>
        <el-table-column label="请求方式" align="center" prop="requestMethod" width="100" />
        <el-table-column label="操作人员" align="center" prop="userName" width="100" />
        <el-table-column label="操作地址" align="center" prop="operIp" width="130" :show-overflow-tooltip="true" />
        <el-table-column label="操作地点" align="center" prop="operLocation" width="130" :show-overflow-tooltip="true" />
        <el-table-column label="操作状态" align="center" prop="status" width="100">
          <template #default="scope">
            <dict-tag :options="statusOptions" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作日期" align="center" prop="operTime" width="160" />
        <el-table-column label="消耗时间" align="center" prop="costTime" width="100">
          <template #default="scope">
            <span>{{ scope.row.costTime }} 毫秒</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              type="text"
              icon="View"
              @click="handleView(scope.row)"
              v-hasPermi="['system:operlog:query']"
            >详细</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 操作日志详细 -->
      <el-dialog title="操作日志详细" v-model="open" width="700px" append-to-body>
        <el-form :model="form" label-width="100px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="操作模块：">{{ form.title }}</el-form-item>
              <el-form-item label="登录信息：">
                {{ form.operIp }} / {{ form.operLocation }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="请求地址：">{{ form.operUrl }}</el-form-item>
              <el-form-item label="请求方式：">{{ form.requestMethod }}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="操作方法：">{{ form.method }}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="请求参数：">{{ form.operParam }}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="返回参数：">{{ form.jsonResult }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="操作人员：">{{ form.userName }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="操作时间：">{{ form.operTime }}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="异常信息：" v-if="form.status === 1">{{ form.errorMsg }}</el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="open = false">关 闭</el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { listOperationLog, getOperationLog, delOperationLog, cleanOperationLog, exportOperationLog } from './api/operation-log'
import type { OperationLogPageResp, OperationLogQueryRequest } from './types/operation-log'
import { BusinessType } from './types/business-type'

// 遮罩层
const loading = ref(false)
// 总条数
const total = ref(0)
// 操作日志表格数据
const operlogList = ref<OperationLogPageResp[]>([])
// 弹出层标题
const title = ref('')
// 是否显示弹出层
const open = ref(false)
// 日期范围
const dateRange = ref<[string, string]>(['', ''])
// 表单参数
const form = ref<OperationLogPageResp>({} as OperationLogPageResp)
// 查询参数
const queryParams = reactive<OperationLogQueryRequest>({
  pageNum: 1,
  pageSize: 10,
  title: undefined,
  businessType: undefined,
  userName: undefined,
  operIp: undefined,
  status: undefined
})

// 操作类型数据字典
const businessTypeOptions = [
  { label: '其它', value: BusinessType.OTHER },
  { label: '新增', value: BusinessType.INSERT },
  { label: '修改', value: BusinessType.UPDATE },
  { label: '删除', value: BusinessType.DELETE },
  { label: '授权', value: BusinessType.GRANT },
  { label: '导出', value: BusinessType.EXPORT },
  { label: '导入', value: BusinessType.IMPORT },
  { label: '强退', value: BusinessType.FORCE_LOGOUT },
  { label: '生成代码', value: BusinessType.GEN_CODE },
  { label: '清空数据', value: BusinessType.CLEAN_DATA }
]

// 操作状态数据字典
const statusOptions = [
  { label: '成功', value: 0 },
  { label: '失败', value: 1 }
]

/** 查询登录日志 */
function getList() {
  loading.value = true
  // 处理日期范围
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.startTime = dateRange.value[0]
    queryParams.endTime = dateRange.value[1]
  } else {
    queryParams.startTime = undefined
    queryParams.endTime = undefined
  }
  
  listOperationLog(queryParams).then(response => {
    operlogList.value = response.data.records
    total.value = response.data.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = ['', '']
  queryParams.title = undefined
  queryParams.businessType = undefined
  queryParams.userName = undefined
  queryParams.operIp = undefined
  queryParams.status = undefined
  queryParams.startTime = undefined
  queryParams.endTime = undefined
  handleQuery()
}

/** 详细按钮操作 */
function handleView(row: OperationLogPageResp) {
  open.value = true
  getOperationLog(row.id).then(response => {
    form.value = response.data
  })
}

/** 删除按钮操作 */
function handleDelete(row: OperationLogPageResp) {
  const ids = [row.id]
  ElMessageBox.confirm('是否确认删除该操作日志?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    return delOperationLog(ids)
  }).then(() => {
    getList()
    ElMessage.success('删除成功')
  }).catch(() => {})
}

/** 清空按钮操作 */
function handleClean() {
  ElMessageBox.confirm('是否确认清空所有操作日志?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    return cleanOperationLog()
  }).then(() => {
    getList()
    ElMessage.success('清空成功')
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  // 处理日期范围
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.startTime = dateRange.value[0]
    queryParams.endTime = dateRange.value[1]
  } else {
    queryParams.startTime = undefined
    queryParams.endTime = undefined
  }
  
  exportOperationLog(queryParams).then(response => {
    // 处理导出逻辑，这里需要根据实际情况调整
    console.log(response)
    ElMessage.success('导出成功')
  })
}

onMounted(() => {
  getList()
})
</script>
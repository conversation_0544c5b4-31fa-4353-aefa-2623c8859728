import type { BusinessType } from './business-type'

/**
 * 操作日志查询请求
 */
export interface OperationLogQueryRequest {
  /** 当前页码 */
  pageNum?: number
  /** 每页条数 */
  pageSize?: number
  /** 模块标题 */
  title?: string
  /** 业务类型 */
  businessType?: BusinessType
  /** 操作人员 */
  userName?: string
  /** 主机地址 */
  operIp?: string
  /** 操作状态（0正常 1异常） */
  status?: number
  /** 开始时间 */
  startTime?: string
  /** 结束时间 */
  endTime?: string
}

/**
 * 操作日志分页响应
 */
export interface OperationLogPageResp {
  /** 日志主键 */
  id: string
  /** 模块标题 */
  title: string
  /** 业务类型 */
  businessType: BusinessType
  /** 方法名称 */
  method: string
  /** 请求方式 */
  requestMethod: string
  /** 操作类别（0其它 1后台用户 2手机端用户） */
  operatorType: number
  /** 操作人员 */
  userName: string
  /** 部门名称 */
  deptName: string
  /** 请求URL */
  operUrl: string
  /** 主机地址 */
  operIp: string
  /** 操作地点 */
  operLocation: string
  /** 请求参数 */
  operParam: string
  /** 返回参数 */
  jsonResult: string
  /** 操作状态（0正常 1异常） */
  status: number
  /** 错误消息 */
  errorMsg: string
  /** 操作时间 */
  operTime: string
  /** 消耗时间（毫秒） */
  costTime: number
}
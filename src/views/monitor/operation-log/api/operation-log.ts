import request from '@/utils/request'
import type { PageResult } from '@/types/global'
import type { OperationLogPageResp, OperationLogQueryRequest } from '../types/operation-log'

/**
 * 查询操作日志列表
 * @param params 查询参数
 * @returns 操作日志列表
 */
export function listOperationLog(params: OperationLogQueryRequest) {
  return request<PageResult<OperationLogPageResp>>({
    url: '/system/operlog/list',
    method: 'get',
    params
  })
}

/**
 * 查询操作日志详细
 * @param id 操作日志ID
 * @returns 操作日志详细信息
 */
export function getOperationLog(id: string) {
  return request({
    url: `/system/operlog/${id}`,
    method: 'get'
  })
}

/**
 * 删除操作日志
 * @param ids 操作日志ID列表
 * @returns 结果
 */
export function delOperationLog(ids: string | string[]) {
  return request({
    url: `/system/operlog/${ids}`,
    method: 'delete'
  })
}

/**
 * 清空操作日志
 * @returns 结果
 */
export function cleanOperationLog() {
  return request({
    url: '/system/operlog/clean',
    method: 'delete'
  })
}

/**
 * 导出操作日志
 * @param params 查询参数
 * @returns 操作日志列表
 */
export function exportOperationLog(params: OperationLogQueryRequest) {
  return request({
    url: '/system/operlog/export',
    method: 'get',
    params
  })
}
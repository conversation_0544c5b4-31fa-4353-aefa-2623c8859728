<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card shadow="never" class="user-info">
          <div class="user-info-top">
            <div class="user-info-img">
              <img :src="user.avatar || defaultAvatar" class="img-circle" />
            </div>
            <div class="user-info-right">
              <div class="user-info-name">{{ user.nickName }}</div>
              <div>{{ roleGroup || '暂无角色' }}</div>
            </div>
          </div>
          <div class="user-info-list">
            <li>
              <el-icon><User /></el-icon>
              用户名称：{{ user.userName }}
            </li>
            <li>
              <el-icon><Phone /></el-icon>
              手机号码：{{ user.phone }}
            </li>
            <li>
              <el-icon><Message /></el-icon>
              用户邮箱：{{ user.email }}
            </li>
            <li>
              <el-icon><Office /></el-icon>
              所属部门：{{ user.deptName }}
            </li>
            <li>
              <el-icon><Clock /></el-icon>
              创建日期：{{ user.createTime }}
            </li>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span>基本资料</span>
            </div>
          </template>
          <el-tabs v-model="activeTab">
            <el-tab-pane label="基本资料" name="userinfo">
              <el-form
                ref="userFormRef"
                :model="userForm"
                :rules="rules"
                label-width="100px"
              >
                <el-form-item label="用户昵称" prop="nickName">
                  <el-input v-model="userForm.nickName" maxlength="30" />
                </el-form-item>
                <el-form-item label="手机号码" prop="phone">
                  <el-input v-model="userForm.phone" maxlength="11" />
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="userForm.email" maxlength="50" />
                </el-form-item>
                <el-form-item label="性别">
                  <el-radio-group v-model="userForm.sex">
                    <el-radio label="0">男</el-radio>
                    <el-radio label="1">女</el-radio>
                    <el-radio label="2">保密</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submit">保存</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="修改密码" name="resetPwd">
              <el-form
                ref="pwdFormRef"
                :model="pwdForm"
                :rules="pwdRules"
                label-width="100px"
              >
                <el-form-item label="旧密码" prop="oldPassword">
                  <el-input
                    v-model="pwdForm.oldPassword"
                    placeholder="请输入旧密码"
                    type="password"
                    show-password
                  />
                </el-form-item>
                <el-form-item label="新密码" prop="newPassword">
                  <el-input
                    v-model="pwdForm.newPassword"
                    placeholder="请输入新密码"
                    type="password"
                    show-password
                  />
                </el-form-item>
                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input
                    v-model="pwdForm.confirmPassword"
                    placeholder="请确认新密码"
                    type="password"
                    show-password
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submitPwd">保存</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="头像修改" name="avatar">
              <el-upload
                class="avatar-uploader"
                action="/api/system/user/profile/avatar"
                :headers="headers"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <img v-if="userForm.avatar" :src="userForm.avatar" class="avatar" />
                <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <div class="avatar-tips">
                <p>建议上传图片尺寸为128*128，大小不超过1MB</p>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, FormInstance } from 'element-plus'
import { User, Phone, Message, Office, Clock, Plus } from '@element-plus/icons-vue'
import { getUserProfile, updateUserProfile, updateUserPwd } from './api/profile'
import { useUserStore } from '@/stores/user-store'
import defaultAvatar from '@/assets/avatar.jpg'

const userStore = useUserStore()
const headers = {
  Authorization: 'Bearer ' + userStore.token
}

// 选项卡切换
const activeTab = ref('userinfo')

// 用户信息
const user = ref({
  userName: '',
  nickName: '',
  email: '',
  phone: '',
  sex: '0',
  avatar: '',
  deptName: '',
  createTime: ''
})

// 角色组
const roleGroup = ref('')

// 表单引用
const userFormRef = ref<FormInstance>()
const pwdFormRef = ref<FormInstance>()

// 用户表单
const userForm = reactive({
  nickName: '',
  phone: '',
  email: '',
  sex: '0',
  avatar: ''
})

// 修改密码表单
const pwdForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单校验规则
const rules = reactive({
  nickName: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
  email: [
    { required: true, message: '邮箱地址不能为空', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
  ],
  phone: [
    { required: true, message: '手机号码不能为空', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
})

// 密码表单校验规则
const pwdRules = reactive({
  oldPassword: [{ required: true, message: '旧密码不能为空', trigger: 'blur' }],
  newPassword: [
    { required: true, message: '新密码不能为空', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '确认密码不能为空', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== pwdForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})

// 获取用户信息
const getUser = async () => {
  try {
    const response = await getUserProfile()
    const { data } = response
    user.value = data
    roleGroup.value = data.roles.join('、')
    userForm.nickName = data.nickName
    userForm.phone = data.phone
    userForm.email = data.email
    userForm.sex = data.sex
    userForm.avatar = data.avatar
  } catch (error) {
    console.error('获取用户信息失败', error)
  }
}

// 提交表单
const submit = () => {
  userFormRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        await updateUserProfile(userForm)
        ElMessage.success('修改成功')
        getUser()
      } catch (error) {
        console.error('修改用户信息失败', error)
      }
    }
  })
}

// 提交密码表单
const submitPwd = () => {
  pwdFormRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        await updateUserPwd(pwdForm.oldPassword, pwdForm.newPassword)
        ElMessage.success('修改成功')
        pwdForm.oldPassword = ''
        pwdForm.newPassword = ''
        pwdForm.confirmPassword = ''
      } catch (error) {
        console.error('修改密码失败', error)
      }
    }
  })
}

// 头像上传前的校验
const beforeAvatarUpload = (file: File) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt1M = file.size / 1024 / 1024 < 1

  if (!isJPG) {
    ElMessage.error('上传头像图片只能是 JPG 或 PNG 格式!')
  }
  if (!isLt1M) {
    ElMessage.error('上传头像图片大小不能超过 1MB!')
  }
  return isJPG && isLt1M
}

// 头像上传成功的回调
const handleAvatarSuccess = (response: any) => {
  if (response.code === 200) {
    userForm.avatar = response.data
    user.value.avatar = response.data
    ElMessage.success('修改成功')
  } else {
    ElMessage.error(response.msg)
  }
}

onMounted(() => {
  getUser()
})
</script>

<style scoped>
.user-info {
  height: 100%;
}

.user-info-top {
  display: flex;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.user-info-img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20px;
}

.user-info-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info-right {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.user-info-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.user-info-list {
  margin-top: 20px;
  padding: 0;
  list-style: none;
}

.user-info-list li {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-size: 14px;
}

.user-info-list li .el-icon {
  margin-right: 10px;
  font-size: 16px;
}

.avatar-uploader {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 128px;
  height: 128px;
  text-align: center;
  line-height: 128px;
}

.avatar {
  width: 128px;
  height: 128px;
  display: block;
}

.avatar-tips {
  text-align: center;
  color: #999;
  font-size: 12px;
}
</style>
import request from '@/utils/request'
import type { Result } from '@/types/global'

/**
 * 获取用户个人信息
 */
export const getUserProfile = (): Promise<Result<any>> => {
  return request({
    url: '/system/user/profile',
    method: 'get'
  })
}

/**
 * 修改用户个人信息
 * @param data 用户信息
 */
export const updateUserProfile = (data: any): Promise<Result<any>> => {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data
  })
}

/**
 * 修改用户密码
 * @param oldPassword 旧密码
 * @param newPassword 新密码
 */
export const updateUserPwd = (oldPassword: string, newPassword: string): Promise<Result<any>> => {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    data
  })
}
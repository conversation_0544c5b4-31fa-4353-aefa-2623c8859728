<template>
  <div class="app-container">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>字典数据 - {{ dictType }}</span>
          <div class="right-menu">
            <el-button type="primary" plain @click="handleAdd" v-hasPermi="['system:dict:add']">
              <el-icon><Plus /></el-icon>新增
            </el-button>
            <el-button @click="handleBack">
              <el-icon><Back /></el-icon>返回
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索区域 -->
      <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
        <el-form-item label="字典标签" prop="dictLabel">
          <el-input v-model="queryParams.dictLabel" placeholder="请输入字典标签" clearable style="width: 240px" @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="字典状态" clearable style="width: 240px">
            <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 表格区域 -->
      <el-table v-loading="loading" :data="dataList">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="字典编码" align="center" prop="id" />
        <el-table-column label="字典标签" align="center" prop="dictLabel" />
        <el-table-column label="字典键值" align="center" prop="dictValue" />
        <el-table-column label="字典排序" align="center" prop="dictSort" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
              {{ scope.row.status === '0' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
        <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:dict:edit']">修改</el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:dict:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <pagination
        v-if="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改字典数据对话框 -->
      <el-dialog :title="title" v-model="open" width="500px" append-to-body>
        <el-form ref="dictDataFormRef" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="字典类型">
            <el-input v-model="form.dictType" :disabled="true" />
          </el-form-item>
          <el-form-item label="字典标签" prop="dictLabel">
            <el-input v-model="form.dictLabel" placeholder="请输入字典标签" />
          </el-form-item>
          <el-form-item label="字典键值" prop="dictValue">
            <el-input v-model="form.dictValue" placeholder="请输入字典键值" />
          </el-form-item>
          <el-form-item label="字典排序" prop="dictSort">
            <el-input-number v-model="form.dictSort" :min="0" controls-position="right" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio v-for="dict in statusOptions" :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="样式属性" prop="cssClass">
            <el-input v-model="form.cssClass" placeholder="请输入样式属性" />
          </el-form-item>
          <el-form-item label="回显样式" prop="listClass">
            <el-select v-model="form.listClass" placeholder="请选择回显样式">
              <el-option label="default" value="default" />
              <el-option label="primary" value="primary" />
              <el-option label="success" value="success" />
              <el-option label="info" value="info" />
              <el-option label="warning" value="warning" />
              <el-option label="danger" value="danger" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否默认" prop="isDefault">
            <el-radio-group v-model="form.isDefault">
              <el-radio v-for="dict in yesNoOptions" :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import { Plus, Search, Refresh, Back } from '@element-plus/icons-vue'
import { listDictData, getDictData, addDictData, updateDictData, deleteDictData } from '../dict/api/dict'
import { statusOptions, yesNoOptions, type DictDataPageResp } from '../dict/types/dict.types.ts'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const dictType = ref(route.params.dictType as string)

// 遮罩层
const loading = ref(false)
// 选中数组
const ids = ref<string[]>([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 总条数
const total = ref(0)
// 字典表格数据
const dataList = ref<DictDataPageResp[]>([])
// 弹出层标题
const title = ref('')
// 是否显示弹出层
const open = ref(false)
// 表单参照对象
const dictDataFormRef = ref<FormInstance>()
const queryFormRef = ref<FormInstance>()

// 表单参数
const form = reactive({
  id: '',
  dictLabel: '',
  dictValue: '',
  dictType: dictType.value,
  dictSort: 0,
  cssClass: '',
  listClass: 'default',
  isDefault: 'N',
  status: '0',
  remark: ''
})

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  dictLabel: '',
  dictType: dictType.value,
  status: ''
})

// 表单校验
const rules = reactive({
  dictLabel: [{ required: true, message: '字典标签不能为空', trigger: 'blur' }],
  dictValue: [{ required: true, message: '字典键值不能为空', trigger: 'blur' }],
  dictSort: [{ required: true, message: '字典排序不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'change' }]
})

/** 查询字典数据列表 */
function getList() {
  loading.value = true
  listDictData(queryParams).then(response => {
    dataList.value = response.data.rows
    total.value = response.data.total
    loading.value = false
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  resetForm()
}

/** 表单重置 */
function resetForm() {
  form.id = ''
  form.dictLabel = ''
  form.dictValue = ''
  form.dictType = dictType.value
  form.dictSort = 0
  form.cssClass = ''
  form.listClass = 'default'
  form.isDefault = 'N'
  form.status = '0'
  form.remark = ''
  dictDataFormRef.value?.resetFields()
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 返回按钮操作 */
function handleBack() {
  router.push('/system/dict')
}

/** 新增按钮操作 */
function handleAdd() {
  resetForm()
  open.value = true
  title.value = '添加字典数据'
}

/** 修改按钮操作 */
function handleUpdate(row: DictDataPageResp) {
  resetForm()
  const id = row.id || ''
  getDictData(id).then(response => {
    Object.assign(form, response.data)
    open.value = true
    title.value = '修改字典数据'
  })
}

/** 提交按钮 */
function submitForm() {
  dictDataFormRef.value?.validate(valid => {
    if (valid) {
      if (form.id !== '') {
        updateDictData(form).then(response => {
          if (response.code === 200) {
            ElMessage.success('修改成功')
            open.value = false
            getList()
          } else {
            ElMessage.error(response.msg)
          }
        })
      } else {
        addDictData(form).then(response => {
          if (response.code === 200) {
            ElMessage.success('新增成功')
            open.value = false
            getList()
          } else {
            ElMessage.error(response.msg)
          }
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row: DictDataPageResp) {
  const dictIds = [row.id]
  ElMessageBox.confirm('是否确认删除字典数据编号为"' + dictIds + '"的数据项?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(function() {
    return deleteDictData(dictIds)
  }).then(() => {
    getList()
    ElMessage.success('删除成功')
  }).catch(() => {})
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.right-menu {
  display: flex;
  gap: 8px;
}
</style>

import type { PageParams } from '@/components/common/api/page.types.ts'

// 字典类型接口定义
export interface DictType {
  id: string
  dictName: string
  dictType: string
  status: string
  remark: string
  createTime: string
  createBy: string
  updateTime: string
  updateBy: string
}

// 字典数据接口定义
export interface DictData {
  id: string
  dictSort: number
  dictLabel: string
  dictValue: string
  dictType: string
  cssClass: string
  listClass: string
  isDefault: string
  status: string
  remark: string
  createTime: string
  createBy: string
  updateTime: string
  updateBy: string
}

// 部门查询参数
export interface DictTypeQueryParams {
  deptName?: string
  status?: number
  parentId?: string
  leader?: string
  phone?: string
  email?: string
}

export interface DictTypeQueryRequest extends PageParams {
  param: DictTypeQueryParams
}

// 字典类型分页响应接口
export interface DictTypePageResp {
  id: string
  dictName: string
  dictType: string
  status: string
  remark: string
  createTime: string
}

// 字典数据分页响应接口
export interface DictDataPageResp {
  id: string
  dictSort: number
  dictLabel: string
  dictValue: string
  dictType: string
  cssClass: string
  listClass: string
  isDefault: string
  status: string
  remark: string
  createTime: string
}

// 状态枚举
export enum StatusEnum {
  NORMAL = '0',
  DISABLE = '1'
}

// 状态选项
export const statusOptions = [
  {
    value: StatusEnum.NORMAL,
    label: '正常'
  },
  {
    value: StatusEnum.DISABLE,
    label: '停用'
  }
]

// 是否默认枚举
export enum YesNoEnum {
  YES = 'Y',
  NO = 'N'
}

// 是否默认选项
export const yesNoOptions = [
  {
    value: YesNoEnum.YES,
    label: '是'
  },
  {
    value: YesNoEnum.NO,
    label: '否'
  }
]

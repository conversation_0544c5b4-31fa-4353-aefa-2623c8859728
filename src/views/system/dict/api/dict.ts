import request from '@/utils/request.ts'

import type { PageResult } from '@/components/common/api/page.types.ts'
import type {
  DictData,
  DictDataPageResp,
  DictType,
  DictTypePageResp,
  DictTypeQueryRequest,
} from '@/views/system/dict/types/dict.types.ts'

// 字典类型API
export const listDictType = async (
  params: DictTypeQueryRequest,
): Promise<PageResult<DictTypePageResp>> => {
  return request.post<PageResult<DictTypePageResp>>('/sys/dict/type/list', params)
}

export const getDictType = (id: string): Promise<DictType> => {
  return request.get<DictType>(`/sys/dict/type/${id}`)
}

export const addDictType = async (data: DictType): Promise<boolean> => {
  return request.post('/sys/dict/type/add', data)
}

export const updateDictType = async (data: DictType): Promise<boolean> => {
  return request.put('/sys/dict/type/edit', data)
}

export const deleteDictType = async (id: string): Promise<boolean> => {
  return request.delete(`/sys/dict/type/delete/${id}`)
}

export const optionselect = (): Promise<Result<DictType[]>> => {
  return request({
    url: '/system/dict/type/optionselect',
    method: 'get',
  })
}

// 字典数据API
export const listDictData = (params: any): Promise<Result<PageResult<DictDataPageResp>>> => {
  return request({
    url: '/system/dict/data/list',
    method: 'get',
    params,
  })
}

export const getDictData = (id: string): Promise<Result<DictData>> => {
  return request({
    url: `/system/dict/data/${id}`,
    method: 'get',
  })
}

export const getDictDataByType = (dictType: string): Promise<Result<DictData[]>> => {
  return request({
    url: `/system/dict/data/type/${dictType}`,
    method: 'get',
  })
}

export const addDictData = (data: any): Promise<Result<boolean>> => {
  return request({
    url: '/system/dict/data',
    method: 'post',
    data,
  })
}

export const updateDictData = (data: any): Promise<Result<boolean>> => {
  return request({
    url: '/system/dict/data',
    method: 'put',
    data,
  })
}

export const deleteDictData = (ids: string[]): Promise<Result<boolean>> => {
  return request({
    url: `/system/dict/data/${ids}`,
    method: 'delete',
  })
}

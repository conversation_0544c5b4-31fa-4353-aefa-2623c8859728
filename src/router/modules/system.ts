import { RouteRecordRaw } from 'vue-router'

/**
 * 系统管理路由
 */
const systemRoutes: RouteRecordRaw[] = [
  {
    path: '/system/dict',
    name: 'Dict',
    component: () => import('@/views/system/dict/index.vue'),
    meta: {
      title: '字典管理',
      icon: 'Dictionary',
      permissions: ['system:dict:list']
    }
  },
  {
    path: '/system/dict-data/index/:dictType',
    name: 'DictData',
    component: () => import('@/views/system/dict-data/index.vue'),
    meta: {
      title: '字典数据',
      icon: 'List',
      hidden: true,
      activeMenu: '/system/dict',
      permissions: ['system:dict:list']
    }
  },
  {
    path: '/system/profile',
    name: 'Profile',
    component: () => import('@/views/system/profile/index.vue'),
    meta: {
      title: '个人中心',
      icon: 'User',
      hidden: true
    }
  }
]

export default systemRoutes
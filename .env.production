# 生产环境配置
# 继承基础配置

# API基础路径
VITE_API_BASE_URL=https://api.yourdomain.com/api

# 是否开启mock数据
VITE_USE_MOCK=false

# 是否开启代理
VITE_USE_PROXY=false

# 是否开启https
VITE_USE_HTTPS=true

# 是否开启gzip压缩
VITE_BUILD_GZIP=true

# 是否删除console
VITE_DROP_CONSOLE=true

# 是否开启source map
VITE_SOURCE_MAP=false

# 构建输出目录
VITE_OUTPUT_DIR=dist

# 静态资源基础路径
VITE_PUBLIC_PATH=/

# 是否开启包分析
VITE_BUILD_ANALYZE=false

# 是否开启CDN
VITE_USE_CDN=false

# CDN基础路径
VITE_CDN_URL=https://cdn.yourdomain.com

# 日志级别
VITE_LOG_LEVEL=error
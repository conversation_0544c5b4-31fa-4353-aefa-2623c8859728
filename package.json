{"name": "chai-vue3-element", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@shene/table": "^1.0.0-alpha.24", "axios": "^1.10.0", "echarts": "^6.0.0", "element-plus": "^2.10.3", "file-saver": "^2.0.5", "json-bigint": "^1.0.0", "lodash": "^4.17.21", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "vue": "^3.5.17", "vue-files-preview": "^1.0.38", "vue-router": "^4.5.1", "vuedraggable": "^2.24.3", "vxe-table": "^4.14.2", "xe-utils": "^3.7.6", "xlsx": "^0.18.5"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/file-saver": "^2.0.7", "@types/json-bigint": "^1.0.4", "@types/lodash": "^4.17.20", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}
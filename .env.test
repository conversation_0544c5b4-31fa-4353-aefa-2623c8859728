# 测试环境配置
# 继承基础配置

# API基础路径
VITE_API_BASE_URL=http://test-api.yourdomain.com/api

# 是否开启mock数据
VITE_USE_MOCK=false

# 是否开启代理
VITE_USE_PROXY=true

# 代理目标地址
VITE_PROXY_TARGET=http://test-api.yourdomain.com

# 是否开启https
VITE_USE_HTTPS=false

# 是否开启gzip压缩
VITE_BUILD_GZIP=true

# 是否删除console
VITE_DROP_CONSOLE=false

# 是否开启source map
VITE_SOURCE_MAP=true

# 构建输出目录
VITE_OUTPUT_DIR=dist-test

# 静态资源基础路径
VITE_PUBLIC_PATH=/

# 是否开启包分析
VITE_BUILD_ANALYZE=false

# 日志级别
VITE_LOG_LEVEL=warn

# 测试相关配置
VITE_TEST_MODE=true
VITE_TEST_TIMEOUT=30000